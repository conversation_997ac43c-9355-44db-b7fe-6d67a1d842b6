package com.ybmmarket20.xyyreport.page.cart

import android.content.Context
import com.ybmmarket20.report.ReportActionProductButtonClickBean
import com.ybmmarket20.report.ReportActionSubModuleClickBean
import com.ybmmarket20.report.ReportActionSubModuleGoodsClickBean
import com.ybmmarket20.report.ReportPageExposureBean
import com.ybmmarket20.report.ReportPageSubModuleGoodsExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.session.SessionManager
import com.ybmmarket20.xyyreport.spm.ScmBean
import com.ybmmarket20.xyyreport.spm.SpmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil
import com.ybmmarket20.xyyreport.spm.XyyReportActivity

object CartReport {

    @JvmStatic
    fun pvCart(context: Context) {
        val spm = SpmUtil.getSpmPv("shoppingCart_0-0_0")
        SpmLogUtil.print("购物车-PV")
        ReportUtil.pvTrack(context, ReportPageExposureBean(), spm)
    }

    private fun trackCartComponentClick(context: Context, spmC: String?, spmD: String?, content: String?) {
        SpmUtil.checkAnalysisContext(context) {
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                this.spmC = spmC
                this.spmD = spmD
            }
            val scm = ScmBean().apply {
                scmA = "appFE"
                scmB = "0"
                scmC = "all_0"
                scmD = "text-$content"
            }
            ReportUtil.clickTrack(context, ReportActionSubModuleClickBean(), spm, scm)
        }
    }

    @JvmStatic
    fun trackCartComponentTopCouponClick(context: Context) {
        SpmLogUtil.print("购物车-顶部-平台券")
        trackCartComponentClick(context, "cartHeader@1", "btn@3", "平台券")
    }

    @JvmStatic
    fun trackCartComponentBottomCouponClick(context: Context) {
        SpmLogUtil.print("购物车-底部-去凑单")
        trackCartComponentClick(context, "ftShopCartCpn@Z", "btn@1", "去凑单")
    }

    @JvmStatic
    fun trackCartComponentToPayClick(context: Context, content: String?) {
        SpmLogUtil.print("购物车-去结算")
        trackCartComponentClick(context, "ftShopCart@Z", "btn@3", content)
    }

    @JvmStatic
    private fun trackCartItemHeaderComponentClick(context: Context, shopCardPosition: Int, btnPosition: Int, subTitlePosition: Int?, shopCode: String?, content: String?) {
        SpmUtil.checkAnalysisContext(context) {
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                spmC = "cartShopList@2"
                spmD = if (subTitlePosition == null) {
                    "shopCard@${shopCardPosition}_btn@$btnPosition"
                } else "shopCard@${shopCardPosition}_subTitle@${subTitlePosition}_btn@$btnPosition"
            }
            val scm = ScmBean(
                "order",
                "0",
                "all_0",
                if (subTitlePosition == null) {
                    "shop-${shopCode}_text-$content"
                } else "shop-${shopCode}_text-$content"
                , null
            )
            ReportUtil.clickTrack(context, ReportActionSubModuleClickBean(), spm, scm)
        }
    }

    @JvmStatic
    fun trackCartShopBtnClick(context: Context, shopCode: String?) {
        SpmLogUtil.print("购物车-店铺按钮点击")
        CartReportDataUtil.getShopInfo(shopCode)?.let {
            trackCartItemHeaderComponentClick(context, it.getShopPosition(), 2, null, shopCode, it.getShopName())
        }
    }

    @JvmStatic
    fun trackCartShopCouponBtnClick(context: Context, shopCode: String?) {
        SpmLogUtil.print("购物车-领券按钮点击")
        CartReportDataUtil.getShopInfo(shopCode)?.let {
            trackCartItemHeaderComponentClick(context, it.getShopPosition(), 3, null, shopCode, "领券")
        }
    }

    @JvmStatic
    fun trackCartShopCouponMakeUpOrder1(context: Context, shopCode: String?, content: String?) {
        SpmLogUtil.print("购物车-凑单页1-按钮点击")
        CartReportDataUtil.getShopInfo(shopCode)?.let {
            trackCartItemHeaderComponentClick(context, it.getShopPosition(), 2, 1, shopCode, content)
        }
    }

    @JvmStatic
    fun trackCartShopCouponMakeUpOrder2(context: Context, shopCode: String?, content: String?) {
        SpmLogUtil.print("购物车-凑单页2-按钮点击")
        CartReportDataUtil.getShopInfo(shopCode)?.let {
            trackCartItemHeaderComponentClick(context, it.getShopPosition(), 2, 2, shopCode, content)
        }
    }

    @JvmStatic
    fun trackCartGoodsExposure(context: Context, prodId: Long) {
        SpmUtil.checkAnalysisContext(context) {
            SpmLogUtil.print("购物车-商品曝光")
            val prodInfo = CartReportDataUtil.getProdInfo(prodId)
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                spmC = "cartShopList@2"
                spmD = "shopCard@${prodInfo?.getShopPosition()}_prodGroup@${prodInfo?.getProdGroupPosition()}_prod@${prodInfo?.getProdPosition()}"
            }
            val scm = ScmBean(
                "order",
                "0",
                "all_0",
                "shop-${prodInfo?.getShopCode()}_prod-${prodInfo?.getProductId()}",
                CartReportDataUtil.getScmId()
            )
            // 移除极光埋点 - page_list_product_exposure (保留搜索页面)
            /*
            val exposureBean = ReportPageSubModuleGoodsExposureBean().apply {
                this.productId = prodId
                this.productName = prodInfo?.getShowName()
            }
            ReportUtil.goodsExposureTrack(context, exposureBean, spm, scm)
            */
        }
    }

    @JvmStatic
    fun trackCartGoodsClick(context: Context, prodId: Long): String {
        val scmE = "${CartReportDataUtil.getScmId()}${SessionManager.get().newGoodsScmRandom()}"
        SpmUtil.checkAnalysisContext(context) {
            SpmLogUtil.print("购物车-商品点击")
            val prodInfo = CartReportDataUtil.getProdInfo(prodId)
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                spmC = "cartShopList@2"
                spmD = "shopCard@${prodInfo?.getShopPosition()}_prodGroup@${prodInfo?.getProdGroupPosition()}_prod@${prodInfo?.getProdPosition()}"
            }
            val scm = ScmBean(
                "order",
                "0",
                "all_0",
                "shop-${prodInfo?.getShopCode()}_prod-${prodInfo?.getProductId()}",
                scmE
            )
            // 移除极光埋点 - action_list_product_click (保留搜索页面)
            /*
            val goodsClick = ReportActionSubModuleGoodsClickBean().apply {
                this.productId = prodId
                this.productName = prodInfo?.getShowName()
            }
            SpmUtil.setSpmE(context, spm)
            ReportUtil.track(context, goodsClick, spm, scm)
            */
        }
        return scmE
    }

    @JvmStatic
    private fun trackCartGoodsBtnClick(context: Context, prodId: Long, content: String?, btnPosition: Int, block: ((XyyReportActivity)-> Unit)? = null) {
        SpmUtil.checkAnalysisContext(context) {
            val prodInfo = CartReportDataUtil.getProdInfo(prodId)
            val scmE = trackCartGoodsClick(context, prodId)
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                spmC = "cartShopList@2"
                spmD = "shopCard@${prodInfo?.getShopPosition()}_prodGroup@${prodInfo?.getProdGroupPosition()}_prod@${prodInfo?.getProdPosition()}_btn@$btnPosition"
            }
            val scm = ScmBean(
                "order",
                "0",
                "all_0",
                "shop-${prodInfo?.getShopCode()}_prod-${prodInfo?.getProductId()}_text-$content",
                scmE
            )
            it.putExtension(CartReportConstant.CART_REPORT_ADD_CART_SPM_CNT, spm)
            it.putExtension(CartReportConstant.CART_REPORT_ADD_CART_SCM_CNT, scm)
            block?.invoke(it)
            val goodsBtnClick = ReportActionProductButtonClickBean().apply {
                productId = prodId
                productName = prodInfo?.getShowName()
            }
            SpmUtil.setSpmE(context, spm)
            ReportUtil.track(context, goodsBtnClick, spm, scm)
        }
    }

    @JvmStatic
    fun trackCartGoodsSubtractBtnClick(context: Context, prodId: Long) {
        trackCartGoodsBtnClick(context, prodId, "减", 1) {
            SpmLogUtil.print("购物车-按钮点击-减号点击")
        }
    }

    @JvmStatic
    fun trackCartGoodsCountBtnClick(context: Context, prodId: Long, count: String?) {
        trackCartGoodsBtnClick(context, prodId, count, 2) {
            SpmLogUtil.print("购物车-按钮点击-数量点击")
        }
    }

    @JvmStatic
    fun trackCartGoodsAddBtnClick(context: Context, prodId: Long) {
        trackCartGoodsBtnClick(context, prodId, "加", 3) {
            SpmLogUtil.print("购物车-按钮点击-加号点击")
        }
    }

    @JvmStatic
    fun trackCartGoodsCollectBtnClick(context: Context, prodId: Long) {
        trackCartGoodsBtnClick(context, prodId, "收藏", 5) {
            CartReportDataUtil.clearAddCartSpmParams(it)
            SpmLogUtil.print("购物车-按钮点击-收藏点击")
        }
    }

    @JvmStatic
    fun trackCartGoodsSameBtnClick(context: Context, prodId: Long) {
        trackCartGoodsBtnClick(context, prodId, "找相似", 6) {
            CartReportDataUtil.clearAddCartSpmParams(it)
            SpmLogUtil.print("购物车-按钮点击-找相似点击")
        }
    }
}